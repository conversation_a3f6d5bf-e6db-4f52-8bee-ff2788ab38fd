import numpy as np
import json

def map_to_3d(features, wall_height=3.0, floor_thickness=0.1, wall_thickness=0.2):
    """
    Convert 2D extracted features to 3D scene data for Three.js
    
    Args:
        features (dict): Dictionary containing extracted 2D features
        wall_height (float): Height of walls in 3D units
        floor_thickness (float): Thickness of floor
        wall_thickness (float): Thickness of walls
    
    Returns:
        dict: 3D scene data compatible with Three.js
    """
    image_shape = features['image_shape']
    scale_factor = features.get('scale_factor', (1.0, 1.0))
    
    # Convert pixel coordinates to 3D world coordinates
    # Assume 1 pixel = 0.1 meters (adjustable)
    pixel_to_meter = 0.1
    
    scene_data = {
        'walls': [],
        'floors': [],
        'doors': [],
        'windows': [],
        'rooms': [],
        'lights': [],
        'camera': {
            'position': [0, 2, 5],
            'target': [0, 0, 0]
        },
        'scene_bounds': {
            'width': image_shape[1] * pixel_to_meter,
            'height': image_shape[0] * pixel_to_meter,
            'wall_height': wall_height
        }
    }
    
    # Convert walls to 3D
    for wall in features['walls']:
        wall_3d = convert_wall_to_3d(
            wall, pixel_to_meter, wall_height, wall_thickness, image_shape
        )
        scene_data['walls'].append(wall_3d)
    
    # Convert rooms to 3D floors
    for room in features['rooms']:
        floor_3d = convert_room_to_floor(
            room, pixel_to_meter, floor_thickness, image_shape
        )
        scene_data['floors'].append(floor_3d)
        
        # Add room label
        room_label = create_room_label(room, pixel_to_meter, image_shape)
        scene_data['rooms'].append(room_label)
    
    # Convert doors to 3D
    for door in features['doors']:
        door_3d = convert_door_to_3d(
            door, pixel_to_meter, wall_height, image_shape
        )
        scene_data['doors'].append(door_3d)
    
    # Convert windows to 3D
    for window in features['windows']:
        window_3d = convert_window_to_3d(
            window, pixel_to_meter, wall_height, image_shape
        )
        scene_data['windows'].append(window_3d)
    
    # Add basic lighting
    scene_data['lights'] = create_default_lighting(scene_data['scene_bounds'])
    
    return scene_data

def convert_wall_to_3d(wall, pixel_to_meter, wall_height, wall_thickness, image_shape):
    """
    Convert a 2D wall segment to 3D wall geometry
    
    Args:
        wall (dict): 2D wall data
        pixel_to_meter (float): Conversion factor
        wall_height (float): Height of the wall
        wall_thickness (float): Thickness of the wall
        image_shape (tuple): Shape of the original image
    
    Returns:
        dict: 3D wall geometry data
    """
    start = wall['start']
    end = wall['end']
    
    # Convert to 3D coordinates (flip Y axis for proper orientation)
    x1 = start[0] * pixel_to_meter
    z1 = (image_shape[0] - start[1]) * pixel_to_meter
    x2 = end[0] * pixel_to_meter
    z2 = (image_shape[0] - end[1]) * pixel_to_meter
    
    # Calculate wall center and dimensions
    center_x = (x1 + x2) / 2
    center_z = (z1 + z2) / 2
    center_y = wall_height / 2
    
    # Calculate wall length and rotation
    length = np.sqrt((x2 - x1)**2 + (z2 - z1)**2)
    rotation_y = np.arctan2(z2 - z1, x2 - x1)
    
    return {
        'type': 'wall',
        'geometry': {
            'type': 'box',
            'width': length,
            'height': wall_height,
            'depth': wall_thickness
        },
        'position': [center_x, center_y, center_z],
        'rotation': [0, rotation_y, 0],
        'material': {
            'color': 0x888888,
            'type': 'MeshLambertMaterial'
        }
    }

def convert_room_to_floor(room, pixel_to_meter, floor_thickness, image_shape):
    """
    Convert a room contour to a 3D floor
    
    Args:
        room (dict): Room data with contour
        pixel_to_meter (float): Conversion factor
        floor_thickness (float): Thickness of the floor
        image_shape (tuple): Shape of the original image
    
    Returns:
        dict: 3D floor geometry data
    """
    bbox = room['bounding_box']
    x, y, w, h = bbox
    
    # Convert to 3D coordinates
    center_x = (x + w/2) * pixel_to_meter
    center_z = (image_shape[0] - (y + h/2)) * pixel_to_meter
    center_y = -floor_thickness / 2
    
    width = w * pixel_to_meter
    depth = h * pixel_to_meter
    
    # Choose floor color based on room type
    floor_colors = {
        'Living Room': 0xDEB887,  # Burlywood
        'Bedroom': 0xF5DEB3,      # Wheat
        'Master Bedroom': 0xD2B48C, # Tan
        'Kitchen': 0xFFFFE0,      # Light yellow
        'Bathroom': 0xE0FFFF,     # Light cyan
        'Hallway': 0xF0F0F0,      # Light gray
        'Closet': 0xDDDDDD        # Light gray
    }
    
    floor_color = floor_colors.get(room['type'], 0xF5F5DC)  # Default beige
    
    return {
        'type': 'floor',
        'room_type': room['type'],
        'geometry': {
            'type': 'box',
            'width': width,
            'height': floor_thickness,
            'depth': depth
        },
        'position': [center_x, center_y, center_z],
        'rotation': [0, 0, 0],
        'material': {
            'color': floor_color,
            'type': 'MeshLambertMaterial'
        }
    }

def convert_door_to_3d(door, pixel_to_meter, wall_height, image_shape):
    """
    Convert a door opening to 3D door geometry
    
    Args:
        door (dict): Door data
        pixel_to_meter (float): Conversion factor
        wall_height (float): Height of walls
        image_shape (tuple): Shape of the original image
    
    Returns:
        dict: 3D door geometry data
    """
    bbox = door['bounding_box']
    x, y, w, h = bbox
    
    # Convert to 3D coordinates
    center_x = (x + w/2) * pixel_to_meter
    center_z = (image_shape[0] - (y + h/2)) * pixel_to_meter
    center_y = wall_height / 2
    
    # Door dimensions (standard door is about 2m high, 0.8m wide)
    door_width = max(w, h) * pixel_to_meter
    door_height = min(wall_height * 0.8, 2.0)  # Don't exceed wall height
    door_thickness = 0.05
    
    return {
        'type': 'door',
        'geometry': {
            'type': 'box',
            'width': door_width,
            'height': door_height,
            'depth': door_thickness
        },
        'position': [center_x, center_y, center_z],
        'rotation': [0, 0, 0],
        'material': {
            'color': 0x8B4513,  # Saddle brown
            'type': 'MeshLambertMaterial'
        }
    }

def convert_window_to_3d(window, pixel_to_meter, wall_height, image_shape):
    """
    Convert a window opening to 3D window geometry
    
    Args:
        window (dict): Window data
        pixel_to_meter (float): Conversion factor
        wall_height (float): Height of walls
        image_shape (tuple): Shape of the original image
    
    Returns:
        dict: 3D window geometry data
    """
    bbox = window['bounding_box']
    x, y, w, h = bbox
    
    # Convert to 3D coordinates
    center_x = (x + w/2) * pixel_to_meter
    center_z = (image_shape[0] - (y + h/2)) * pixel_to_meter
    center_y = wall_height * 0.6  # Windows are typically higher up
    
    # Window dimensions
    window_width = w * pixel_to_meter
    window_height = h * pixel_to_meter
    window_thickness = 0.02
    
    return {
        'type': 'window',
        'geometry': {
            'type': 'box',
            'width': window_width,
            'height': window_height,
            'depth': window_thickness
        },
        'position': [center_x, center_y, center_z],
        'rotation': [0, 0, 0],
        'material': {
            'color': 0x87CEEB,  # Sky blue
            'type': 'MeshLambertMaterial',
            'transparent': True,
            'opacity': 0.7
        }
    }

def create_room_label(room, pixel_to_meter, image_shape):
    """
    Create a 3D text label for a room
    
    Args:
        room (dict): Room data
        pixel_to_meter (float): Conversion factor
        image_shape (tuple): Shape of the original image
    
    Returns:
        dict: 3D text label data
    """
    center = room['center']
    
    # Convert to 3D coordinates
    x = center[0] * pixel_to_meter
    z = (image_shape[0] - center[1]) * pixel_to_meter
    y = 0.1  # Slightly above the floor
    
    return {
        'type': 'text',
        'text': room['type'],
        'position': [x, y, z],
        'rotation': [-np.pi/2, 0, 0],  # Lay flat on the floor
        'material': {
            'color': 0x000000,  # Black text
            'type': 'MeshBasicMaterial'
        },
        'font_size': 0.3
    }

def create_default_lighting(scene_bounds):
    """
    Create default lighting for the 3D scene
    
    Args:
        scene_bounds (dict): Scene boundary information
    
    Returns:
        list: List of light configurations
    """
    lights = []
    
    # Ambient light
    lights.append({
        'type': 'ambient',
        'color': 0x404040,
        'intensity': 0.4
    })
    
    # Directional light (sun)
    lights.append({
        'type': 'directional',
        'color': 0xffffff,
        'intensity': 0.8,
        'position': [scene_bounds['width']/2, scene_bounds['wall_height']*2, scene_bounds['height']/2],
        'target': [scene_bounds['width']/2, 0, scene_bounds['height']/2],
        'cast_shadow': True
    })
    
    # Point lights for indoor illumination
    lights.append({
        'type': 'point',
        'color': 0xffffff,
        'intensity': 0.5,
        'position': [scene_bounds['width']/4, scene_bounds['wall_height']*0.8, scene_bounds['height']/4],
        'distance': scene_bounds['width']
    })
    
    lights.append({
        'type': 'point',
        'color': 0xffffff,
        'intensity': 0.5,
        'position': [scene_bounds['width']*3/4, scene_bounds['wall_height']*0.8, scene_bounds['height']*3/4],
        'distance': scene_bounds['width']
    })
    
    return lights

def optimize_scene_data(scene_data):
    """
    Optimize scene data for better performance
    
    Args:
        scene_data (dict): Raw scene data
    
    Returns:
        dict: Optimized scene data
    """
    # Remove duplicate walls
    scene_data['walls'] = remove_duplicate_walls(scene_data['walls'])
    
    # Merge adjacent floors of the same type
    scene_data['floors'] = merge_adjacent_floors(scene_data['floors'])
    
    return scene_data

def remove_duplicate_walls(walls, tolerance=0.1):
    """
    Remove duplicate wall segments
    
    Args:
        walls (list): List of wall data
        tolerance (float): Position tolerance for duplicates
    
    Returns:
        list: Deduplicated walls
    """
    unique_walls = []
    
    for wall in walls:
        is_duplicate = False
        for existing_wall in unique_walls:
            if walls_are_similar(wall, existing_wall, tolerance):
                is_duplicate = True
                break
        
        if not is_duplicate:
            unique_walls.append(wall)
    
    return unique_walls

def walls_are_similar(wall1, wall2, tolerance):
    """
    Check if two walls are similar (potential duplicates)
    
    Args:
        wall1, wall2 (dict): Wall data
        tolerance (float): Position tolerance
    
    Returns:
        bool: True if walls are similar
    """
    pos1 = wall1['position']
    pos2 = wall2['position']
    
    # Check position similarity
    pos_diff = np.sqrt(sum((a - b)**2 for a, b in zip(pos1, pos2)))
    
    if pos_diff < tolerance:
        # Check geometry similarity
        geom1 = wall1['geometry']
        geom2 = wall2['geometry']
        
        size_diff = abs(geom1['width'] - geom2['width']) + abs(geom1['height'] - geom2['height'])
        
        return size_diff < tolerance
    
    return False

def merge_adjacent_floors(floors):
    """
    Merge adjacent floors of the same type (simplified)
    
    Args:
        floors (list): List of floor data
    
    Returns:
        list: Merged floors
    """
    # For now, just return the original floors
    # This could be enhanced to actually merge adjacent floors
    return floors
