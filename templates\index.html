<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Based 2D Blueprint to 3D Walkthrough</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .upload-section {
            margin: 40px 0;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px 20px;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-text {
            color: #333;
            font-size: 1.2em;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #666;
            font-size: 0.9em;
        }

        #file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .features {
            margin-top: 40px;
            text-align: left;
        }

        .features h3 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 30px;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
            font-size: 1.2em;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .flash-messages {
            margin-bottom: 20px;
        }

        .flash-message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .flash-error {
            background: #ffe6e6;
            color: #d63031;
            border: 1px solid #fab1a0;
        }

        .flash-success {
            background: #e6ffe6;
            color: #00b894;
            border: 1px solid #55a3ff;
        }

        .preview-area {
            margin-top: 20px;
            display: none;
        }

        .preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ Blueprint to 3D</h1>
            <p>Transform your 2D architectural blueprints into immersive 3D walkthroughs using AI-powered computer vision and deep learning.</p>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                <div class="flash-messages">
                    {% for message in messages %}
                        <div class="flash-message flash-error">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Upload Form -->
        <form id="upload-form" action="/upload" method="post" enctype="multipart/form-data">
            <div class="upload-section">
                <div class="upload-area" id="upload-area">
                    <div class="upload-icon">📋</div>
                    <div class="upload-text">Drop your blueprint here or click to browse</div>
                    <div class="upload-subtext">Supports PNG, JPG, JPEG files (max 16MB)</div>
                    <input type="file" id="file-input" name="file" accept=".png,.jpg,.jpeg" required>
                </div>
                
                <div class="preview-area" id="preview-area">
                    <img id="preview-image" class="preview-image" alt="Preview">
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Processing your blueprint... This may take a few moments.</p>
                </div>
                
                <button type="submit" class="btn" id="submit-btn">🚀 Generate 3D Walkthrough</button>
            </div>
        </form>

        <!-- Gallery Link -->
        <a href="/gallery" class="btn">📁 View Gallery</a>

        <!-- Features Section -->
        <div class="features">
            <h3>🎯 What We Do</h3>
            <ul class="feature-list">
                <li>🔍 Advanced image preprocessing with OpenCV</li>
                <li>🧠 AI-powered feature extraction using deep learning</li>
                <li>🏠 Automatic detection of walls, doors, windows, and rooms</li>
                <li>📐 Intelligent 2D to 3D coordinate mapping</li>
                <li>🎮 Interactive 3D walkthrough with Three.js</li>
                <li>💡 Realistic lighting and material rendering</li>
            </ul>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const previewArea = document.getElementById('preview-area');
        const previewImage = document.getElementById('preview-image');
        const uploadForm = document.getElementById('upload-form');
        const submitBtn = document.getElementById('submit-btn');
        const loading = document.getElementById('loading');

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        // Handle file selection
        function handleFileSelect(file) {
            // Validate file type
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a PNG, JPG, or JPEG file.');
                return;
            }

            // Validate file size (16MB)
            if (file.size > 16 * 1024 * 1024) {
                alert('File size must be less than 16MB.');
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImage.src = e.target.result;
                previewArea.style.display = 'block';
            };
            reader.readAsDataURL(file);

            // Update upload area text
            uploadArea.querySelector('.upload-text').textContent = `Selected: ${file.name}`;
            uploadArea.querySelector('.upload-subtext').textContent = `Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
        }

        // Form submission
        uploadForm.addEventListener('submit', (e) => {
            if (!fileInput.files.length) {
                e.preventDefault();
                alert('Please select a file first.');
                return;
            }

            // Show loading
            submitBtn.style.display = 'none';
            loading.style.display = 'block';
        });
    </script>
</body>
</html>
