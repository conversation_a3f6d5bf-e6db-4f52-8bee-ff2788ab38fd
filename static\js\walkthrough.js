// 3D Walkthrough Scene Manager
class SceneManager {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.sceneData = null;
        this.objects = {
            walls: [],
            floors: [],
            doors: [],
            windows: [],
            lights: []
        };
        this.wireframeMode = false;
        this.cameraSpeed = 1.0;
        
        // Movement state
        this.moveForward = false;
        this.moveBackward = false;
        this.moveLeft = false;
        this.moveRight = false;
        this.moveUp = false;
        this.moveDown = false;
        
        this.velocity = new THREE.Vector3();
        this.direction = new THREE.Vector3();
        
        this.init();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        const container = document.getElementById('scene-container');
        container.appendChild(this.renderer.domElement);

        // Setup controls
        this.setupControls();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Start render loop
        this.animate();
    }

    setupControls() {
        // Use PointerLockControls for FPS-style movement
        this.controls = new THREE.PointerLockControls(this.camera, document.body);
        
        // Add controls to scene
        this.scene.add(this.controls.getObject());
        
        // Lock pointer on click
        document.addEventListener('click', () => {
            if (document.pointerLockElement !== document.body) {
                this.controls.lock();
            }
        });
    }

    setupEventListeners() {
        // Keyboard controls
        document.addEventListener('keydown', (event) => {
            switch (event.code) {
                case 'ArrowUp':
                case 'KeyW':
                    this.moveForward = true;
                    break;
                case 'ArrowLeft':
                case 'KeyA':
                    this.moveLeft = true;
                    break;
                case 'ArrowDown':
                case 'KeyS':
                    this.moveBackward = true;
                    break;
                case 'ArrowRight':
                case 'KeyD':
                    this.moveRight = true;
                    break;
                case 'Space':
                    this.moveUp = true;
                    event.preventDefault();
                    break;
                case 'ShiftLeft':
                case 'ShiftRight':
                    this.moveDown = true;
                    break;
            }
        });

        document.addEventListener('keyup', (event) => {
            switch (event.code) {
                case 'ArrowUp':
                case 'KeyW':
                    this.moveForward = false;
                    break;
                case 'ArrowLeft':
                case 'KeyA':
                    this.moveLeft = false;
                    break;
                case 'ArrowDown':
                case 'KeyS':
                    this.moveBackward = false;
                    break;
                case 'ArrowRight':
                case 'KeyD':
                    this.moveRight = false;
                    break;
                case 'Space':
                    this.moveUp = false;
                    break;
                case 'ShiftLeft':
                case 'ShiftRight':
                    this.moveDown = false;
                    break;
            }
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }

    async loadSceneData(dataFile) {
        try {
            const response = await fetch(`/api/scene-data/${dataFile}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.sceneData = await response.json();
            this.buildScene();
            
            // Update UI
            if (window.updateSceneInfo) {
                window.updateSceneInfo(this.sceneData);
            }
            
            // Hide loading screen
            if (window.hideLoadingScreen) {
                window.hideLoadingScreen();
            }
        } catch (error) {
            console.error('Error loading scene data:', error);
            if (window.showError) {
                window.showError('Failed to load scene data. Please try again.');
            }
        }
    }

    buildScene() {
        // Clear existing objects
        this.clearScene();

        // Build walls
        this.sceneData.walls.forEach(wallData => {
            const wall = this.createWall(wallData);
            this.scene.add(wall);
            this.objects.walls.push(wall);
        });

        // Build floors
        this.sceneData.floors.forEach(floorData => {
            const floor = this.createFloor(floorData);
            this.scene.add(floor);
            this.objects.floors.push(floor);
        });

        // Build doors
        this.sceneData.doors.forEach(doorData => {
            const door = this.createDoor(doorData);
            this.scene.add(door);
            this.objects.doors.push(door);
        });

        // Build windows
        this.sceneData.windows.forEach(windowData => {
            const window = this.createWindow(windowData);
            this.scene.add(window);
            this.objects.windows.push(window);
        });

        // Add lighting
        this.setupLighting();

        // Position camera
        this.resetCamera();
    }

    createWall(wallData) {
        const geometry = new THREE.BoxGeometry(
            wallData.geometry.width,
            wallData.geometry.height,
            wallData.geometry.depth
        );
        
        const material = new THREE.MeshLambertMaterial({
            color: wallData.material.color
        });

        const wall = new THREE.Mesh(geometry, material);
        wall.position.set(...wallData.position);
        wall.rotation.set(...wallData.rotation);
        wall.castShadow = true;
        wall.receiveShadow = true;
        wall.userData = { type: 'wall' };

        return wall;
    }

    createFloor(floorData) {
        const geometry = new THREE.BoxGeometry(
            floorData.geometry.width,
            floorData.geometry.height,
            floorData.geometry.depth
        );
        
        const material = new THREE.MeshLambertMaterial({
            color: floorData.material.color
        });

        const floor = new THREE.Mesh(geometry, material);
        floor.position.set(...floorData.position);
        floor.rotation.set(...floorData.rotation);
        floor.receiveShadow = true;
        floor.userData = { type: 'floor', roomType: floorData.room_type };

        return floor;
    }

    createDoor(doorData) {
        const geometry = new THREE.BoxGeometry(
            doorData.geometry.width,
            doorData.geometry.height,
            doorData.geometry.depth
        );
        
        const material = new THREE.MeshLambertMaterial({
            color: doorData.material.color
        });

        const door = new THREE.Mesh(geometry, material);
        door.position.set(...doorData.position);
        door.rotation.set(...doorData.rotation);
        door.castShadow = true;
        door.userData = { type: 'door' };

        return door;
    }

    createWindow(windowData) {
        const geometry = new THREE.BoxGeometry(
            windowData.geometry.width,
            windowData.geometry.height,
            windowData.geometry.depth
        );
        
        const material = new THREE.MeshLambertMaterial({
            color: windowData.material.color,
            transparent: windowData.material.transparent || false,
            opacity: windowData.material.opacity || 1.0
        });

        const window = new THREE.Mesh(geometry, material);
        window.position.set(...windowData.position);
        window.rotation.set(...windowData.rotation);
        window.userData = { type: 'window' };

        return window;
    }

    setupLighting() {
        // Clear existing lights
        this.objects.lights.forEach(light => {
            this.scene.remove(light);
        });
        this.objects.lights = [];

        // Add lights from scene data
        this.sceneData.lights.forEach(lightData => {
            let light;

            switch (lightData.type) {
                case 'ambient':
                    light = new THREE.AmbientLight(lightData.color, lightData.intensity);
                    break;
                case 'directional':
                    light = new THREE.DirectionalLight(lightData.color, lightData.intensity);
                    light.position.set(...lightData.position);
                    if (lightData.cast_shadow) {
                        light.castShadow = true;
                        light.shadow.mapSize.width = 2048;
                        light.shadow.mapSize.height = 2048;
                    }
                    break;
                case 'point':
                    light = new THREE.PointLight(
                        lightData.color, 
                        lightData.intensity, 
                        lightData.distance || 0
                    );
                    light.position.set(...lightData.position);
                    break;
            }

            if (light) {
                this.scene.add(light);
                this.objects.lights.push(light);
            }
        });
    }

    clearScene() {
        // Remove all objects
        Object.values(this.objects).forEach(objectArray => {
            objectArray.forEach(obj => {
                this.scene.remove(obj);
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) obj.material.dispose();
            });
        });

        // Reset object arrays
        this.objects = {
            walls: [],
            floors: [],
            doors: [],
            windows: [],
            lights: []
        };
    }

    updateMovement() {
        const delta = 0.016; // Assume 60fps
        const speed = this.cameraSpeed * 5.0;

        this.velocity.x -= this.velocity.x * 10.0 * delta;
        this.velocity.z -= this.velocity.z * 10.0 * delta;
        this.velocity.y -= this.velocity.y * 10.0 * delta;

        this.direction.z = Number(this.moveForward) - Number(this.moveBackward);
        this.direction.x = Number(this.moveRight) - Number(this.moveLeft);
        this.direction.y = Number(this.moveUp) - Number(this.moveDown);
        this.direction.normalize();

        if (this.moveForward || this.moveBackward) {
            this.velocity.z -= this.direction.z * speed * delta;
        }
        if (this.moveLeft || this.moveRight) {
            this.velocity.x -= this.direction.x * speed * delta;
        }
        if (this.moveUp || this.moveDown) {
            this.velocity.y += this.direction.y * speed * delta;
        }

        this.controls.moveRight(-this.velocity.x * delta);
        this.controls.moveForward(-this.velocity.z * delta);
        this.controls.getObject().position.y += this.velocity.y * delta;

        // Update camera position display
        if (window.updateCameraInfo) {
            window.updateCameraInfo(this.controls.getObject().position);
        }
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        if (this.controls.isLocked) {
            this.updateMovement();
        }

        this.renderer.render(this.scene, this.camera);
    }

    // Control methods
    resetCamera() {
        if (this.sceneData && this.sceneData.camera) {
            this.controls.getObject().position.set(...this.sceneData.camera.position);
        } else {
            this.controls.getObject().position.set(0, 2, 5);
        }
    }

    setTopView() {
        if (this.sceneData && this.sceneData.scene_bounds) {
            const bounds = this.sceneData.scene_bounds;
            this.controls.getObject().position.set(
                bounds.width / 2,
                bounds.wall_height * 2,
                bounds.height / 2
            );
        }
    }

    updateWallHeight(height) {
        this.objects.walls.forEach(wall => {
            wall.scale.y = height / 3.0; // Assuming original height is 3.0
        });
    }

    updateLighting(intensity) {
        this.objects.lights.forEach(light => {
            if (light.intensity !== undefined) {
                light.intensity = intensity;
            }
        });
    }

    updateCameraSpeed(speed) {
        this.cameraSpeed = speed;
    }

    toggleWireframe() {
        this.wireframeMode = !this.wireframeMode;
        
        [...this.objects.walls, ...this.objects.floors, ...this.objects.doors, ...this.objects.windows].forEach(obj => {
            if (obj.material) {
                obj.material.wireframe = this.wireframeMode;
            }
        });
    }

    exportScene() {
        const sceneJson = JSON.stringify(this.sceneData, null, 2);
        const blob = new Blob([sceneJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = 'scene_data.json';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }
}

// Global scene manager instance
window.sceneManager = null;

// Initialize scene function
function initializeScene(dataFile) {
    window.sceneManager = new SceneManager();
    window.sceneManager.loadSceneData(dataFile);
}
