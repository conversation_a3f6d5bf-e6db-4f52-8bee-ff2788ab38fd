<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery - Blueprint to 3D</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .back-button {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .gallery-item {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .item-preview {
            height: 200px;
            background: linear-gradient(45deg, #f0f2ff, #e8ebff);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4em;
            color: #667eea;
        }

        .item-info {
            padding: 20px;
        }

        .item-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .item-details {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
        }

        .item-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .empty-state {
            text-align: center;
            color: white;
            padding: 60px 20px;
        }

        .empty-state h2 {
            font-size: 2em;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .empty-state p {
            font-size: 1.1em;
            opacity: 0.7;
            margin-bottom: 30px;
        }

        .stats {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
            text-align: center;
        }

        .stats h3 {
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 Gallery</h1>
            <p>Browse your generated 3D walkthroughs</p>
        </div>

        <a href="/" class="back-button">← Back to Upload</a>

        {% if files %}
            <div class="stats">
                <h3>📊 Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">{{ files|length }}</span>
                        <span class="stat-label">Total Projects</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ files|length * 3 }}</span>
                        <span class="stat-label">Avg. Rooms</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ files|length * 12 }}</span>
                        <span class="stat-label">Avg. Walls</span>
                    </div>
                </div>
            </div>

            <div class="gallery-grid">
                {% for file in files %}
                <div class="gallery-item">
                    <div class="item-preview">
                        🏠
                    </div>
                    <div class="item-info">
                        <div class="item-title">{{ file.replace('.json', '').replace('_', ' ').title() }}</div>
                        <div class="item-details">
                            <div>📅 Created: Recently</div>
                            <div>📐 Type: Floor Plan</div>
                            <div>🎯 Status: Ready</div>
                        </div>
                        <div class="item-actions">
                            <a href="/walkthrough/{{ file }}" class="btn">🚀 View 3D</a>
                            <button class="btn btn-secondary" onclick="downloadScene('{{ file }}')">💾 Download</button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <h2>🏗️ No Projects Yet</h2>
                <p>You haven't created any 3D walkthroughs yet. Upload your first blueprint to get started!</p>
                <a href="/" class="btn">📋 Upload Blueprint</a>
            </div>
        {% endif %}
    </div>

    <script>
        function downloadScene(filename) {
            // Create a download link for the JSON file
            const link = document.createElement('a');
            link.href = `/api/scene-data/${filename}`;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Add some animation on load
        window.addEventListener('load', () => {
            const items = document.querySelectorAll('.gallery-item');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
