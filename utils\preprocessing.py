import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

def preprocess_image(image_path, target_size=(512, 512)):
    """
    Comprehensive preprocessing pipeline for blueprint images
    
    Args:
        image_path (str): Path to the input image
        target_size (tuple): Target size for resizing (width, height)
    
    Returns:
        dict: Dictionary containing processed images and metadata
    """
    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    original_shape = image.shape
    
    # Convert to RGB for consistency
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Step 1: Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Step 2: Resize image while maintaining aspect ratio
    resized = resize_with_aspect_ratio(gray, target_size)
    
    # Step 3: Apply Gaussian Blur to reduce noise
    blurred = cv2.GaussianBlur(resized, (5, 5), 0)
    
    # Step 4: Apply Canny Edge Detection
    edges = cv2.Canny(blurred, 50, 150, apertureSize=3)
    
    # Step 5: Apply Adaptive Thresholding
    thresh = cv2.adaptiveThreshold(
        blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
    )
    
    # Step 6: Morphological operations to clean up the image
    kernel = np.ones((3, 3), np.uint8)
    
    # Remove noise
    opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=2)
    
    # Fill gaps
    closing = cv2.morphologyEx(opening, cv2.MORPH_CLOSE, kernel, iterations=2)
    
    # Step 7: Invert if necessary (walls should be black, background white)
    if np.mean(closing) > 127:
        closing = cv2.bitwise_not(closing)
        thresh = cv2.bitwise_not(thresh)
    
    return {
        'original': image_rgb,
        'grayscale': gray,
        'resized': resized,
        'blurred': blurred,
        'edges': edges,
        'threshold': thresh,
        'cleaned': closing,
        'original_shape': original_shape,
        'processed_shape': resized.shape,
        'scale_factor': (original_shape[1] / target_size[0], original_shape[0] / target_size[1])
    }

def resize_with_aspect_ratio(image, target_size):
    """
    Resize image while maintaining aspect ratio
    
    Args:
        image (np.array): Input image
        target_size (tuple): Target size (width, height)
    
    Returns:
        np.array: Resized image
    """
    h, w = image.shape[:2]
    target_w, target_h = target_size
    
    # Calculate scaling factor
    scale = min(target_w / w, target_h / h)
    
    # Calculate new dimensions
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    # Resize image
    resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
    
    # Create a new image with target size and place resized image in center
    result = np.ones((target_h, target_w), dtype=np.uint8) * 255
    
    # Calculate padding
    pad_x = (target_w - new_w) // 2
    pad_y = (target_h - new_h) // 2
    
    # Place resized image in center
    result[pad_y:pad_y + new_h, pad_x:pad_x + new_w] = resized
    
    return result

def enhance_lines(image):
    """
    Enhance line detection in blueprint images
    
    Args:
        image (np.array): Input grayscale image
    
    Returns:
        np.array: Enhanced image with better line visibility
    """
    # Apply different kernels to detect horizontal and vertical lines
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))
    
    # Detect horizontal lines
    horizontal_lines = cv2.morphologyEx(image, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)
    
    # Detect vertical lines
    vertical_lines = cv2.morphologyEx(image, cv2.MORPH_OPEN, vertical_kernel, iterations=2)
    
    # Combine horizontal and vertical lines
    lines_combined = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)
    
    return lines_combined

def remove_text_and_symbols(image):
    """
    Remove text and small symbols from blueprint images
    
    Args:
        image (np.array): Input binary image
    
    Returns:
        np.array: Image with text and symbols removed
    """
    # Find contours
    contours, _ = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Create a mask for large structural elements
    mask = np.zeros_like(image)
    
    for contour in contours:
        area = cv2.contourArea(contour)
        # Keep only large contours (likely to be walls, rooms)
        if area > 100:  # Adjust threshold as needed
            cv2.fillPoly(mask, [contour], 255)
    
    return mask

def save_preprocessing_results(processed_data, output_dir):
    """
    Save preprocessing results for debugging and visualization
    
    Args:
        processed_data (dict): Dictionary containing processed images
        output_dir (str): Directory to save results
    """
    import os
    os.makedirs(output_dir, exist_ok=True)
    
    # Save each processed image
    for key, image in processed_data.items():
        if isinstance(image, np.ndarray) and len(image.shape) == 2:
            cv2.imwrite(os.path.join(output_dir, f"{key}.png"), image)
        elif isinstance(image, np.ndarray) and len(image.shape) == 3:
            cv2.imwrite(os.path.join(output_dir, f"{key}.png"), cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
