import cv2
import numpy as np
from sklearn.cluster import DBSCAN
import json

def extract_features(processed_data):
    """
    Extract architectural features from preprocessed blueprint images
    
    Args:
        processed_data (dict): Dictionary containing processed images
    
    Returns:
        dict: Dictionary containing extracted features
    """
    cleaned_image = processed_data['cleaned']
    edges = processed_data['edges']
    
    # Extract different types of features
    walls = extract_walls(cleaned_image, edges)
    rooms = extract_rooms(cleaned_image)
    openings = extract_openings(cleaned_image, walls)
    
    # Classify openings into doors and windows (simplified heuristic)
    doors, windows = classify_openings(openings, cleaned_image)
    
    return {
        'walls': walls,
        'rooms': rooms,
        'doors': doors,
        'windows': windows,
        'image_shape': cleaned_image.shape,
        'scale_factor': processed_data.get('scale_factor', (1.0, 1.0))
    }

def extract_walls(image, edges):
    """
    Extract wall segments from the image using line detection
    
    Args:
        image (np.array): Cleaned binary image
        edges (np.array): Edge-detected image
    
    Returns:
        list: List of wall segments with coordinates
    """
    walls = []
    
    # Use HoughLinesP to detect line segments
    lines = cv2.HoughLinesP(
        edges, 
        rho=1, 
        theta=np.pi/180, 
        threshold=50, 
        minLineLength=30, 
        maxLineGap=10
    )
    
    if lines is not None:
        for line in lines:
            x1, y1, x2, y2 = line[0]
            
            # Calculate line properties
            length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
            angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi
            
            # Filter out very short lines
            if length > 20:
                walls.append({
                    'start': [int(x1), int(y1)],
                    'end': [int(x2), int(y2)],
                    'length': float(length),
                    'angle': float(angle),
                    'type': 'wall'
                })
    
    # Merge nearby parallel lines
    walls = merge_parallel_walls(walls)
    
    return walls

def merge_parallel_walls(walls, angle_threshold=10, distance_threshold=20):
    """
    Merge nearby parallel wall segments
    
    Args:
        walls (list): List of wall segments
        angle_threshold (float): Maximum angle difference for parallel lines
        distance_threshold (float): Maximum distance for merging
    
    Returns:
        list: Merged wall segments
    """
    if not walls:
        return walls
    
    merged_walls = []
    used = set()
    
    for i, wall1 in enumerate(walls):
        if i in used:
            continue
            
        current_wall = wall1.copy()
        used.add(i)
        
        for j, wall2 in enumerate(walls[i+1:], i+1):
            if j in used:
                continue
                
            # Check if walls are parallel and close
            angle_diff = abs(wall1['angle'] - wall2['angle'])
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
                
            if angle_diff < angle_threshold:
                # Check distance between lines
                dist = point_to_line_distance(
                    wall2['start'], wall1['start'], wall1['end']
                )
                
                if dist < distance_threshold:
                    # Merge the walls by extending the line
                    current_wall = extend_wall(current_wall, wall2)
                    used.add(j)
        
        merged_walls.append(current_wall)
    
    return merged_walls

def extract_rooms(image):
    """
    Extract room regions using contour detection
    
    Args:
        image (np.array): Cleaned binary image
    
    Returns:
        list: List of room regions with properties
    """
    rooms = []
    
    # Find contours
    contours, _ = cv2.findContours(
        cv2.bitwise_not(image), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
    )
    
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        
        # Filter out small areas (likely noise)
        if area > 500:  # Adjust threshold as needed
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Calculate center
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
            else:
                cx, cy = x + w//2, y + h//2
            
            # Approximate room type based on aspect ratio
            aspect_ratio = w / h if h > 0 else 1
            room_type = classify_room_by_shape(area, aspect_ratio)
            
            rooms.append({
                'id': i,
                'contour': contour.tolist(),
                'center': [int(cx), int(cy)],
                'bounding_box': [int(x), int(y), int(w), int(h)],
                'area': float(area),
                'aspect_ratio': float(aspect_ratio),
                'type': room_type
            })
    
    return rooms

def extract_openings(image, walls):
    """
    Extract door and window openings from the image
    
    Args:
        image (np.array): Cleaned binary image
        walls (list): List of wall segments
    
    Returns:
        list: List of opening regions
    """
    openings = []
    
    # Create a wall mask
    wall_mask = np.zeros_like(image)
    for wall in walls:
        cv2.line(
            wall_mask, 
            tuple(wall['start']), 
            tuple(wall['end']), 
            255, 
            thickness=3
        )
    
    # Find gaps in walls (potential openings)
    # Dilate wall mask to find nearby gaps
    kernel = np.ones((5, 5), np.uint8)
    dilated_walls = cv2.dilate(wall_mask, kernel, iterations=1)
    
    # Find contours in the gaps
    gaps = cv2.bitwise_and(cv2.bitwise_not(image), cv2.bitwise_not(dilated_walls))
    contours, _ = cv2.findContours(gaps, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        
        # Filter openings by size
        if 50 < area < 2000:  # Adjust thresholds as needed
            x, y, w, h = cv2.boundingRect(contour)
            
            openings.append({
                'id': i,
                'contour': contour.tolist(),
                'bounding_box': [int(x), int(y), int(w), int(h)],
                'area': float(area),
                'center': [int(x + w//2), int(y + h//2)]
            })
    
    return openings

def classify_openings(openings, image):
    """
    Classify openings into doors and windows based on size and position
    
    Args:
        openings (list): List of opening regions
        image (np.array): Original image for context
    
    Returns:
        tuple: (doors, windows) lists
    """
    doors = []
    windows = []
    
    for opening in openings:
        area = opening['area']
        bbox = opening['bounding_box']
        w, h = bbox[2], bbox[3]
        aspect_ratio = w / h if h > 0 else 1
        
        # Simple heuristic classification
        # Doors are typically larger and more rectangular
        # Windows are smaller and can be more square
        
        if area > 200 and (aspect_ratio > 0.3 and aspect_ratio < 3.0):
            # Likely a door
            opening['type'] = 'door'
            opening['width'] = float(max(w, h))  # Door width
            opening['height'] = float(min(w, h))  # Door thickness
            doors.append(opening)
        else:
            # Likely a window
            opening['type'] = 'window'
            opening['width'] = float(w)
            opening['height'] = float(h)
            windows.append(opening)
    
    return doors, windows

def classify_room_by_shape(area, aspect_ratio):
    """
    Classify room type based on area and aspect ratio
    
    Args:
        area (float): Room area in pixels
        aspect_ratio (float): Width to height ratio
    
    Returns:
        str: Estimated room type
    """
    # Simple heuristic classification
    if area > 5000:
        if aspect_ratio > 1.5:
            return "Living Room"
        else:
            return "Master Bedroom"
    elif area > 2000:
        if aspect_ratio > 1.3:
            return "Kitchen"
        else:
            return "Bedroom"
    elif area > 1000:
        if aspect_ratio > 1.5:
            return "Hallway"
        else:
            return "Bathroom"
    else:
        return "Closet"

def point_to_line_distance(point, line_start, line_end):
    """
    Calculate distance from a point to a line segment
    
    Args:
        point (list): [x, y] coordinates
        line_start (list): [x, y] coordinates of line start
        line_end (list): [x, y] coordinates of line end
    
    Returns:
        float: Distance from point to line
    """
    x0, y0 = point
    x1, y1 = line_start
    x2, y2 = line_end
    
    # Calculate distance using cross product formula
    num = abs((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1)
    den = np.sqrt((y2 - y1)**2 + (x2 - x1)**2)
    
    return num / den if den > 0 else float('inf')

def extend_wall(wall1, wall2):
    """
    Extend wall1 to include wall2
    
    Args:
        wall1 (dict): First wall segment
        wall2 (dict): Second wall segment
    
    Returns:
        dict: Extended wall segment
    """
    # Find the extreme points
    points = [wall1['start'], wall1['end'], wall2['start'], wall2['end']]
    
    # Project all points onto the line and find extremes
    # For simplicity, just take the bounding box extremes
    xs = [p[0] for p in points]
    ys = [p[1] for p in points]
    
    # Determine if line is more horizontal or vertical
    if abs(wall1['angle']) < 45 or abs(wall1['angle']) > 135:
        # More horizontal
        min_x, max_x = min(xs), max(xs)
        # Use average y coordinate
        avg_y = sum(ys) // len(ys)
        new_start = [min_x, avg_y]
        new_end = [max_x, avg_y]
    else:
        # More vertical
        min_y, max_y = min(ys), max(ys)
        # Use average x coordinate
        avg_x = sum(xs) // len(xs)
        new_start = [avg_x, min_y]
        new_end = [avg_x, max_y]
    
    # Calculate new properties
    length = np.sqrt((new_end[0] - new_start[0])**2 + (new_end[1] - new_start[1])**2)
    angle = np.arctan2(new_end[1] - new_start[1], new_end[0] - new_start[0]) * 180 / np.pi
    
    return {
        'start': new_start,
        'end': new_end,
        'length': float(length),
        'angle': float(angle),
        'type': 'wall'
    }
