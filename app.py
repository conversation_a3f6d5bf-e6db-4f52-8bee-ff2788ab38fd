import os
import json
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename
import cv2
import numpy as np
from utils.preprocessing import preprocess_image
from utils.feature_extraction import extract_features
from utils.mapping_to_3d import map_to_3d

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# Configuration
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Main page with upload form"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and processing"""
    if 'file' not in request.files:
        flash('No file selected')
        return redirect(request.url)
    
    file = request.files['file']
    
    if file.filename == '':
        flash('No file selected')
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        try:
            # Process the uploaded image
            processed_data = process_blueprint(filepath)
            
            # Store the processed data for the walkthrough
            json_filename = filename.rsplit('.', 1)[0] + '.json'
            json_filepath = os.path.join(app.config['UPLOAD_FOLDER'], json_filename)
            
            with open(json_filepath, 'w') as f:
                json.dump(processed_data, f)
            
            return redirect(url_for('walkthrough', filename=json_filename))
            
        except Exception as e:
            flash(f'Error processing image: {str(e)}')
            return redirect(url_for('index'))
    
    flash('Invalid file type. Please upload PNG, JPG, or JPEG files.')
    return redirect(url_for('index'))

@app.route('/walkthrough/<filename>')
def walkthrough(filename):
    """Display 3D walkthrough"""
    json_filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    
    if not os.path.exists(json_filepath):
        flash('Processed data not found')
        return redirect(url_for('index'))
    
    return render_template('walkthrough.html', data_file=filename)

@app.route('/api/scene-data/<filename>')
def get_scene_data(filename):
    """API endpoint to get 3D scene data"""
    json_filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    
    if not os.path.exists(json_filepath):
        return jsonify({'error': 'Data not found'}), 404
    
    with open(json_filepath, 'r') as f:
        data = json.load(f)
    
    return jsonify(data)

def process_blueprint(image_path):
    """Main processing pipeline for blueprint images"""
    # Step 1: Preprocess the image
    processed_img = preprocess_image(image_path)
    
    # Step 2: Extract features
    features = extract_features(processed_img)
    
    # Step 3: Map to 3D
    scene_data = map_to_3d(features)
    
    return scene_data

@app.route('/gallery')
def gallery():
    """Show gallery of processed blueprints"""
    json_files = [f for f in os.listdir(app.config['UPLOAD_FOLDER']) if f.endswith('.json')]
    return render_template('gallery.html', files=json_files)

if __name__ == '__main__':
    print("Starting Flask application...")
    print("Server will be available at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
