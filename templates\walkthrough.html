<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Walkthrough - Blueprint to 3D</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }

        #scene-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .controls-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            z-index: 1000;
            max-width: 300px;
        }

        .controls-panel h3 {
            margin-bottom: 15px;
            color: #667eea;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9em;
            color: #ccc;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }

        .control-group button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 2px;
            font-size: 0.9em;
        }

        .control-group button:hover {
            opacity: 0.8;
        }

        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            z-index: 1000;
            max-width: 250px;
        }

        .info-panel h3 {
            margin-bottom: 15px;
            color: #667eea;
        }

        .info-item {
            margin-bottom: 10px;
            font-size: 0.9em;
        }

        .info-item strong {
            color: #fff;
        }

        .navigation-help {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            z-index: 1000;
            font-size: 0.8em;
        }

        .navigation-help h4 {
            margin-bottom: 10px;
            color: #667eea;
        }

        .nav-instruction {
            margin-bottom: 5px;
            color: #ccc;
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a1a;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .spinner {
            border: 4px solid #333;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            color: #ff6b6b;
            text-align: center;
            padding: 20px;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 10px;
            margin: 20px;
        }

        .back-button {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            z-index: 1000;
            font-size: 1em;
        }

        .back-button:hover {
            opacity: 0.8;
        }

        .toggle-panel {
            position: absolute;
            top: 80px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: none;
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1000;
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="scene-container">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="spinner"></div>
            <h2>Generating 3D Scene...</h2>
            <p>Processing your blueprint and creating the walkthrough</p>
        </div>

        <!-- Back Button -->
        <a href="/" class="back-button">← Back to Upload</a>

        <!-- Toggle Controls Button -->
        <button id="toggle-controls" class="toggle-panel">🎛️</button>

        <!-- Controls Panel -->
        <div id="controls-panel" class="controls-panel">
            <h3>🎮 Scene Controls</h3>
            
            <div class="control-group">
                <label>Wall Height</label>
                <input type="range" id="wall-height" min="2" max="5" value="3" step="0.1">
                <span id="wall-height-value">3.0m</span>
            </div>

            <div class="control-group">
                <label>Lighting Intensity</label>
                <input type="range" id="lighting" min="0.1" max="2" value="1" step="0.1">
                <span id="lighting-value">1.0</span>
            </div>

            <div class="control-group">
                <label>Camera Speed</label>
                <input type="range" id="camera-speed" min="0.5" max="3" value="1" step="0.1">
                <span id="camera-speed-value">1.0</span>
            </div>

            <div class="control-group">
                <button id="reset-camera">🎯 Reset Camera</button>
                <button id="top-view">🔝 Top View</button>
                <button id="toggle-wireframe">📐 Wireframe</button>
            </div>

            <div class="control-group">
                <button id="export-scene">💾 Export Scene</button>
                <button id="fullscreen">🖥️ Fullscreen</button>
            </div>
        </div>

        <!-- Info Panel -->
        <div id="info-panel" class="info-panel">
            <h3>📊 Scene Info</h3>
            <div class="info-item">
                <strong>Walls:</strong> <span id="wall-count">-</span>
            </div>
            <div class="info-item">
                <strong>Rooms:</strong> <span id="room-count">-</span>
            </div>
            <div class="info-item">
                <strong>Doors:</strong> <span id="door-count">-</span>
            </div>
            <div class="info-item">
                <strong>Windows:</strong> <span id="window-count">-</span>
            </div>
            <div class="info-item">
                <strong>Camera:</strong> <span id="camera-position">-</span>
            </div>
        </div>

        <!-- Navigation Help -->
        <div class="navigation-help">
            <h4>🕹️ Navigation</h4>
            <div class="nav-instruction">🖱️ <strong>Mouse:</strong> Look around</div>
            <div class="nav-instruction">⌨️ <strong>WASD:</strong> Move around</div>
            <div class="nav-instruction">🔄 <strong>Scroll:</strong> Zoom in/out</div>
            <div class="nav-instruction">⬆️ <strong>Space:</strong> Move up</div>
            <div class="nav-instruction">⬇️ <strong>Shift:</strong> Move down</div>
        </div>
    </div>

    <!-- Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/PointerLockControls.js"></script>
    <script src="/static/js/walkthrough.js"></script>
    
    <script>
        // Initialize the 3D scene
        const dataFile = "{{ data_file }}";
        
        // UI Controls
        const loadingScreen = document.getElementById('loading-screen');
        const controlsPanel = document.getElementById('controls-panel');
        const toggleControlsBtn = document.getElementById('toggle-controls');
        
        // Toggle controls panel
        toggleControlsBtn.addEventListener('click', () => {
            controlsPanel.classList.toggle('hidden');
        });

        // Control event listeners
        document.getElementById('wall-height').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('wall-height-value').textContent = value.toFixed(1) + 'm';
            if (window.sceneManager) {
                window.sceneManager.updateWallHeight(value);
            }
        });

        document.getElementById('lighting').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('lighting-value').textContent = value.toFixed(1);
            if (window.sceneManager) {
                window.sceneManager.updateLighting(value);
            }
        });

        document.getElementById('camera-speed').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('camera-speed-value').textContent = value.toFixed(1);
            if (window.sceneManager) {
                window.sceneManager.updateCameraSpeed(value);
            }
        });

        document.getElementById('reset-camera').addEventListener('click', () => {
            if (window.sceneManager) {
                window.sceneManager.resetCamera();
            }
        });

        document.getElementById('top-view').addEventListener('click', () => {
            if (window.sceneManager) {
                window.sceneManager.setTopView();
            }
        });

        document.getElementById('toggle-wireframe').addEventListener('click', () => {
            if (window.sceneManager) {
                window.sceneManager.toggleWireframe();
            }
        });

        document.getElementById('export-scene').addEventListener('click', () => {
            if (window.sceneManager) {
                window.sceneManager.exportScene();
            }
        });

        document.getElementById('fullscreen').addEventListener('click', () => {
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                document.documentElement.requestFullscreen();
            }
        });

        // Initialize scene when page loads
        window.addEventListener('load', () => {
            initializeScene(dataFile);
        });

        function updateSceneInfo(sceneData) {
            document.getElementById('wall-count').textContent = sceneData.walls.length;
            document.getElementById('room-count').textContent = sceneData.rooms.length;
            document.getElementById('door-count').textContent = sceneData.doors.length;
            document.getElementById('window-count').textContent = sceneData.windows.length;
        }

        function updateCameraInfo(position) {
            const pos = `(${position.x.toFixed(1)}, ${position.y.toFixed(1)}, ${position.z.toFixed(1)})`;
            document.getElementById('camera-position').textContent = pos;
        }

        function hideLoadingScreen() {
            loadingScreen.style.display = 'none';
        }

        function showError(message) {
            loadingScreen.innerHTML = `
                <div class="error-message">
                    <h2>❌ Error Loading Scene</h2>
                    <p>${message}</p>
                    <a href="/" class="back-button" style="position: relative; top: 20px;">← Back to Upload</a>
                </div>
            `;
        }
    </script>
</body>
</html>
